# Credit Chakra - Development Guide

## 🚀 Quick Start Commands

### Start Everything (Recommended)
```bash
# One command to rule them all!
./start.sh                    # macOS/Linux
start.bat                     # Windows
npm start                     # Cross-platform
```

### Individual Services
```bash
# Backend only
npm run backend

# Frontend only  
npm run frontend

# Setup dependencies
npm run setup
```

## 📁 Project Structure

```
credit-chakra/
├── 🔧 Backend (FastAPI)
│   ├── main.py              # Server entry point
│   ├── routes/              # API endpoints
│   ├── models/              # Data models
│   ├── services/            # Business logic
│   ├── requirements.txt     # Python dependencies
│   └── venv/               # Virtual environment
│
├── 🎨 Frontend (Next.js)
│   ├── src/
│   │   ├── app/            # Pages (App Router)
│   │   ├── components/     # React components
│   │   ├── lib/           # API client & utilities
│   │   └── types/         # TypeScript definitions
│   ├── package.json       # Node dependencies
│   └── .env.local         # Environment variables
│
└── 🛠️ Scripts
    ├── start.sh           # Unix startup script
    ├── start.bat          # Windows startup script
    └── package.json       # Root package config
```

## 🌐 Application URLs

| Service | URL | Description |
|---------|-----|-------------|
| **Analytics Dashboard** | http://localhost:3000 | Main dashboard with portfolio overview |
| **Portfolio Management** | http://localhost:3000/msmes | MSME listing and management |
| **Backend API** | http://localhost:8000 | FastAPI REST endpoints |
| **API Documentation** | http://localhost:8000/docs | Interactive Swagger docs |
| **API Health Check** | http://localhost:8000/health | Server status endpoint |

## 🔧 Development Workflow

### 1. First Time Setup
```bash
# Clone and setup
git clone <repository-url>
cd credit-chakra
npm run setup
```

### 2. Daily Development
```bash
# Start everything
./start.sh

# Make changes to code
# Both servers auto-reload on file changes

# Stop servers
Ctrl+C
```

### 3. Testing
```bash
# Backend tests
npm run test

# Frontend linting
npm run lint
```

## 📊 Key Features

### Analytics Dashboard (Root Page)
- **Portfolio Overview**: Total MSMEs, average scores, risk distribution
- **Visual Charts**: Risk bands, business type distribution
- **Quick Navigation**: Click "Total MSMEs" to view full portfolio
- **Real-time Data**: Live updates from backend API

### Portfolio Management
- **MSME Listing**: Searchable and filterable table
- **Individual Profiles**: Detailed MSME information
- **Credit Scoring**: Real-time score calculation
- **Risk Assessment**: Green/Yellow/Red classification

### Backend API
- **RESTful Design**: Standard HTTP methods and status codes
- **Real-time Processing**: Live data updates
- **Mock Data**: Development-ready sample data
- **CORS Enabled**: Frontend integration ready

## 🛠️ Development Tips

### Hot Reload
- **Frontend**: Automatic reload on file changes
- **Backend**: Automatic restart on Python file changes
- **Environment**: Changes to .env files require restart

### Debugging
- **Frontend**: Browser DevTools, React DevTools
- **Backend**: FastAPI automatic docs at /docs
- **API Testing**: Use /docs interface or curl commands

### Code Organization
- **Components**: Reusable UI components in `frontend/src/components/`
- **Pages**: Route-based pages in `frontend/src/app/`
- **API Routes**: Backend endpoints in `backend/routes/`
- **Types**: Shared TypeScript types in `frontend/src/types/`

## 🔄 Common Commands

```bash
# Clean everything and start fresh
npm run clean
npm run setup
npm start

# View logs
# Backend logs appear in terminal
# Frontend logs in browser console

# Stop specific services
# Use Ctrl+C in the startup script terminal
# Or kill processes manually:
lsof -ti :3000 | xargs kill -9  # Frontend
lsof -ti :8000 | xargs kill -9  # Backend
```

## 🚨 Troubleshooting

### Port Issues
```bash
# Check what's using ports
lsof -i :3000
lsof -i :8000

# Kill processes
npm run clean
```

### Dependency Issues
```bash
# Python dependencies
cd backend
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Node dependencies
cd frontend
rm -rf node_modules
npm install
```

### Environment Issues
```bash
# Check environment files
cat frontend/.env.local
cat backend/.env

# Verify API connection
curl http://localhost:8000/health
```

## 📝 Next Steps

1. **Add Features**: Extend API endpoints and UI components
2. **Database**: Replace mock data with real database
3. **Authentication**: Add user login and permissions
4. **Testing**: Expand test coverage
5. **Deployment**: Configure for production environment

## 🎯 Navigation Flow

```
Analytics Dashboard (/) 
    ↓ Click "Total MSMEs"
Portfolio Page (/msmes)
    ↓ Click any MSME row
MSME Detail Page (/msme/[id])
    ↓ Click "Analytics" tab
MSME Analytics (/msme/[id]/analytics)
```

Happy coding! 🚀
