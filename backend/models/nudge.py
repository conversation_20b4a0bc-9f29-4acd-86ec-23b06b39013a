from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime
from enum import Enum

class TriggerType(str, Enum):
    SCORE_DROP = "score_drop"
    SCORE_IMPROVEMENT = "score_improvement"
    NEW_SIGNAL = "new_signal"
    PERIODIC_UPDATE = "periodic_update"
    RISK_BAND_CHANGE = "risk_band_change"

class Medium(str, Enum):
    WHATSAPP = "whatsapp"
    EMAIL = "email"
    SMS = "sms"
    PUSH_NOTIFICATION = "push_notification"

class NudgeStatus(str, Enum):
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    DELIVERED = "delivered"

class Nudge(BaseModel):
    nudge_id: Optional[str] = None
    msme_id: str = Field(..., min_length=1)
    trigger_type: TriggerType
    message: str = Field(..., min_length=1, max_length=1000)
    medium: Medium
    sent_at: Optional[datetime] = None
    status: NudgeStatus = NudgeStatus.PENDING
    metadata: Optional[dict] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class NudgeCreate(BaseModel):
    msme_id: str = Field(..., min_length=1)
    trigger_type: TriggerType
    message: str = Field(..., min_length=1, max_length=1000)
    medium: Medium
    metadata: Optional[dict] = Field(default_factory=dict)

class NudgeUpdate(BaseModel):
    message: Optional[str] = Field(None, min_length=1, max_length=1000)
    status: Optional[NudgeStatus] = None
    metadata: Optional[dict] = None
