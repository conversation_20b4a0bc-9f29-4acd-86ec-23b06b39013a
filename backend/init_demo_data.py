#!/usr/bin/env python3
"""
Initialize demo data for Credit Chakra platform
"""
from firebase.init import _MOCK_DATA_STORE
from datetime import datetime
import uuid

def init_demo_data():
    """Initialize the mock data store with sample MSMEs and signals"""
    
    # Sample MSMEs with calculated scores
    msmes = [
        {
            "msme_id": "tech-solutions-001",
            "name": "Tech Solutions Pvt Ltd",
            "business_type": "services",
            "location": "Bangalore, Karnataka",
            "created_at": "2025-01-01T10:00:00",
            "score": 659.0,  # Calculated from signals
            "risk_band": "yellow",
            "tags": ["technology", "software", "startup"]
        },
        {
            "msme_id": "mumbai-textiles-002",
            "name": "Mumbai Textiles Co",
            "business_type": "manufacturing",
            "location": "Mumbai, Maharashtra",
            "created_at": "2025-01-01T10:15:00",
            "score": 801.0,  # High performer
            "risk_band": "green",
            "tags": ["textiles", "export", "established"]
        },
        {
            "msme_id": "green-grocers-003",
            "name": "Green Grocers Delhi",
            "business_type": "retail",
            "location": "Delhi, NCR",
            "created_at": "2025-01-01T10:30:00",
            "score": 331.0,  # Low performer
            "risk_band": "red",
            "tags": ["grocery", "organic", "local"]
        },
        {
            "msme_id": "autoparts-supply-004",
            "name": "AutoParts Supply Chain",
            "business_type": "b2b",
            "location": "Chennai, Tamil Nadu",
            "created_at": "2025-01-01T10:45:00",
            "score": 630.0,  # Medium performer
            "risk_band": "yellow",
            "tags": ["automotive", "supply-chain", "b2b"]
        },
        {
            "msme_id": "pune-food-005",
            "name": "Pune Food Processing",
            "business_type": "manufacturing",
            "location": "Pune, Maharashtra",
            "created_at": "2025-01-01T11:00:00",
            "score": 416.0,  # Medium performer
            "risk_band": "yellow",
            "tags": ["food-processing", "fmcg", "regional"]
        }
    ]
    
    # Add MSMEs to store
    for msme in msmes:
        _MOCK_DATA_STORE['msmes'][msme['msme_id']] = msme
    
    # Sample signals for each MSME
    signals = [
        # Tech Solutions signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "tech-solutions-001",
            "source": "gst",
            "value": 2500000,
            "normalized": 0.45,
            "timestamp": "2025-01-01T12:00:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "tech-solutions-001",
            "source": "upi",
            "value": {"transaction_count": 450, "volume": 1800000},
            "normalized": 0.725,
            "timestamp": "2025-01-01T12:05:00",
            "metadata": {"period": "Q4-2024"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "tech-solutions-001",
            "source": "reviews",
            "value": {"average_rating": 4.3, "review_count": 87},
            "normalized": 0.804,
            "timestamp": "2025-01-01T12:10:00",
            "metadata": {"platform": "Google Reviews"}
        },
        
        # Mumbai Textiles signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "mumbai-textiles-002",
            "source": "gst",
            "value": 8500000,
            "normalized": 0.805,
            "timestamp": "2025-01-01T12:15:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "mumbai-textiles-002",
            "source": "reviews",
            "value": {"average_rating": 4.7, "review_count": 156},
            "normalized": 0.940,
            "timestamp": "2025-01-01T12:20:00",
            "metadata": {"platform": "Google Reviews"}
        },
        
        # Green Grocers signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "green-grocers-003",
            "source": "gst",
            "value": 650000,
            "normalized": 0.195,
            "timestamp": "2025-01-01T12:25:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "green-grocers-003",
            "source": "reviews",
            "value": {"average_rating": 3.8, "review_count": 23},
            "normalized": 0.467,
            "timestamp": "2025-01-01T12:30:00",
            "metadata": {"platform": "Google Reviews"}
        },
        
        # AutoParts signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "autoparts-supply-004",
            "source": "gst",
            "value": 4200000,
            "normalized": 0.620,
            "timestamp": "2025-01-01T12:35:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "autoparts-supply-004",
            "source": "upi",
            "value": {"transaction_count": 280, "volume": 950000},
            "normalized": 0.640,
            "timestamp": "2025-01-01T12:40:00",
            "metadata": {"period": "Q4-2024"}
        },
        
        # Pune Food signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "pune-food-005",
            "source": "gst",
            "value": 3200000,
            "normalized": 0.520,
            "timestamp": "2025-01-01T12:45:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        }
    ]
    
    # Add signals to store using nested structure
    for signal in signals:
        msme_id = signal['msme_id']
        signal_id = signal['signal_id']
        
        # Create nested structure: msmes/{msme_id}/signals/{signal_id}
        nested_key = f"msmes/{msme_id}/signals"
        if nested_key not in _MOCK_DATA_STORE:
            _MOCK_DATA_STORE[nested_key] = {}
        _MOCK_DATA_STORE[nested_key][signal_id] = signal
    
    print(f"✅ Initialized demo data:")
    print(f"   📊 {len(msmes)} MSMEs")
    print(f"   📈 {len(signals)} Signals")
    print(f"   🎯 Risk distribution: {sum(1 for m in msmes if m['risk_band'] == 'green')} Green, {sum(1 for m in msmes if m['risk_band'] == 'yellow')} Yellow, {sum(1 for m in msmes if m['risk_band'] == 'red')} Red")

if __name__ == "__main__":
    init_demo_data()
