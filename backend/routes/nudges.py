from fastapi import APIRouter, HTTPException, status
from typing import List, Optional
from datetime import datetime
import uuid

from models.nudge import Nudge, NudgeCreate, NudgeUpdate, NudgeStatus
from firebase.init import get_firestore_client

router = APIRouter()

@router.post("/send", response_model=Nudge, status_code=status.HTTP_201_CREATED)
async def send_nudge(nudge_data: NudgeCreate):
    """Send a nudge to an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(nudge_data.msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Generate unique nudge ID
        nudge_id = str(uuid.uuid4())
        
        # Create nudge
        nudge = Nudge(
            nudge_id=nudge_id,
            msme_id=nudge_data.msme_id,
            trigger_type=nudge_data.trigger_type,
            message=nudge_data.message,
            medium=nudge_data.medium,
            sent_at=datetime.utcnow(),
            status=NudgeStatus.SENT,  # For now, assume immediate sending
            metadata=nudge_data.metadata
        )
        
        # Save nudge to Firestore
        nudge_ref = db.collection('msmes').document(nudge_data.msme_id).collection('nudges').document(nudge_id)
        nudge_ref.set(nudge.dict())
        
        # TODO: Implement actual sending logic based on medium
        # For now, just mark as sent
        
        return nudge
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send nudge: {str(e)}"
        )

@router.get("/{msme_id}", response_model=List[Nudge])
async def get_nudges(msme_id: str, status_filter: Optional[str] = None, limit: int = 50):
    """Get nudges for an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Build query
        query = db.collection('msmes').document(msme_id).collection('nudges')
        
        if status_filter:
            query = query.where('status', '==', status_filter)
        
        query = query.order_by('sent_at', direction='DESCENDING').limit(limit)
        
        # Execute query
        docs = query.stream()
        
        nudges = []
        for doc in docs:
            data = doc.to_dict()
            nudges.append(Nudge(**data))
        
        return nudges
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve nudges: {str(e)}"
        )

@router.put("/{msme_id}/{nudge_id}", response_model=Nudge)
async def update_nudge(msme_id: str, nudge_id: str, update_data: NudgeUpdate):
    """Update nudge status or content"""
    try:
        db = get_firestore_client()
        
        nudge_ref = db.collection('msmes').document(msme_id).collection('nudges').document(nudge_id)
        nudge_doc = nudge_ref.get()
        
        if not nudge_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nudge not found"
            )
        
        # Update only provided fields
        update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
        nudge_ref.update(update_dict)
        
        # Return updated nudge
        updated_doc = nudge_ref.get()
        data = updated_doc.to_dict()
        return Nudge(**data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update nudge: {str(e)}"
        )
