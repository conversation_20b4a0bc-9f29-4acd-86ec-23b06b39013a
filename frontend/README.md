# Credit Chakra Frontend

Next.js + Chakra UI dashboard for Credit Chakra MSME credit management platform.

## Features

### UI 1 - Portfolio View
- **Grid/List of MSMEs**: Clean card-based layout showing all MSMEs
- **Risk Band Colors**: Visual color coding (Green/Yellow/Red) for quick risk assessment
- **Score Display**: Prominent credit score with trend indicators
- **Location Info**: Business location and type indicators
- **Click to Detail**: Navigate to detailed MSME view
- **Search & Filters**: Filter by risk band, business type, and search by name/location

### UI 2 - MSME Detail View
- **Score Timeline**: Visual representation of credit score changes over time
- **Signal Events Log**: Chronological list of all data signals (GST, UPI, Reviews, etc.)
- **Nudge History**: Complete history of sent notifications with status tracking
- **Manual Nudge Trigger**: Send custom notifications via WhatsApp, Email, or SMS
- **Score Breakdown**: Detailed analysis of score components and penalties

## Technology Stack

- **Framework**: Next.js 15 with App Router
- **UI Library**: Chakra UI for consistent design system
- **Language**: TypeScript for type safety
- **Charts**: Recharts for data visualization
- **Date Handling**: date-fns for date formatting
- **API Client**: Custom fetch-based client with error handling

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Credit Chakra backend running on port 8000

### Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment**:
   ```bash
   cp .env.local.example .env.local
   ```

   Update `.env.local` with your backend URL:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:8000
   ```

3. **Run development server**:
   ```bash
   npm run dev
   ```

4. **Open browser**:
   Navigate to `http://localhost:3000`
