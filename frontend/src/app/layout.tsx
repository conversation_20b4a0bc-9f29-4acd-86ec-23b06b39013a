import { ChakraProvider } from "@/providers/ChakraProvider";
import Navigation from "@/components/Navigation";
import "./globals.css";

export const metadata = {
  title: "Credit Chakra - MSME Credit Manager",
  description: "Credit scoring and monitoring platform for MSMEs",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body suppressHydrationWarning>
        <ChakraProvider>
          <Navigation />
          {children}
        </ChakraProvider>
      </body>
    </html>
  );
}
