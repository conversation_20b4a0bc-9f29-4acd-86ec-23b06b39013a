'use client';

import { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Badge,
  HStack,
  VStack,
  Spinner,
  Alert,
  AlertIcon,
  Button,
  Flex,
  Icon,
  Input,
  Select,
  InputGroup,
  InputLeftElement,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  IconButton,
  SimpleGrid,
} from '@chakra-ui/react';
import { useRouter } from 'next/navigation';
import { MSMEProfile } from '@/types';
import { apiClient } from '@/lib/api';
import {
  getRiskBandColor,
  getRiskBandColorScheme,
  getRiskBandBg,
  formatScore,
  formatDate,
  getBusinessTypeIcon,
  getTrendIcon,
  getTrendColor,
} from '@/lib/utils';
import { Icon as LucideIcon } from '@/components/ui/Icon';

export default function PortfolioPage() {
  const [msmes, setMsmes] = useState<MSMEProfile[]>([]);
  const [filteredMsmes, setFilteredMsmes] = useState<MSMEProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [riskFilter, setRiskFilter] = useState('all');
  const [businessTypeFilter, setBusinessTypeFilter] = useState('all');
  
  const router = useRouter();
  const cardBg = 'white';
  const borderColor = 'gray.200';

  useEffect(() => {
    fetchPortfolio();
  }, []);

  useEffect(() => {
    filterMsmes();
  }, [msmes, searchTerm, riskFilter, businessTypeFilter]);

  const fetchPortfolio = async () => {
    try {
      setLoading(true);
      console.log('Fetching portfolio data...');
      const data = await apiClient.getPortfolio();
      console.log('Portfolio data received:', data);
      setMsmes(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching portfolio:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch portfolio');
    } finally {
      setLoading(false);
    }
  };

  const filterMsmes = () => {
    let filtered = msmes;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(msme =>
        msme.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        msme.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Risk band filter
    if (riskFilter !== 'all') {
      filtered = filtered.filter(msme => msme.risk_band === riskFilter);
    }

    // Business type filter
    if (businessTypeFilter !== 'all') {
      filtered = filtered.filter(msme => msme.business_type === businessTypeFilter);
    }

    setFilteredMsmes(filtered);
  };

  const handleMSMEClick = (msmeId: string) => {
    router.push(`/msme/${msmeId}`);
  };

  if (loading) {
    return (
      <Box bg="neutral.50" minH="100vh">
        <Container maxW="container.xl" py={8}>
          <Flex justify="center" align="center" minH="400px">
            <VStack spacing={6}>
              <Box
                w={16}
                h={16}
                bg="brand.100"
                borderRadius="full"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Spinner size="lg" color="brand.600" thickness="3px" />
              </Box>
              <VStack spacing={2}>
                <Text fontSize="lg" fontWeight="600" color="neutral.700">
                  Loading Portfolio
                </Text>
                <Text fontSize="sm" color="neutral.500">
                  Fetching your MSME data...
                </Text>
              </VStack>
            </VStack>
          </Flex>
        </Container>
      </Box>
    );
  }

  if (error) {
    return (
      <Box bg="neutral.50" minH="100vh">
        <Container maxW="container.xl" py={8}>
          <Box
            bg="white"
            p={8}
            borderRadius="xl"
            shadow="sm"
            border="1px solid"
            borderColor="danger.200"
          >
            <Alert status="error" bg="danger.50" borderRadius="lg">
              <AlertIcon color="danger.600" />
              <VStack align="start" spacing={2}>
                <Text fontWeight="600" color="danger.800">
                  Failed to load portfolio
                </Text>
                <Text fontSize="sm" color="danger.600">
                  {error}
                </Text>
              </VStack>
            </Alert>
          </Box>
        </Container>
      </Box>
    );
  }

  return (
    <Box bg="neutral.50" minH="100vh">
      <Container maxW="container.xl" py={8}>
        {/* Header */}
        <Flex justify="space-between" align="center" mb={8}>
          <VStack align="start" spacing={3}>
            <Heading size="xl" color="neutral.800" fontWeight="700">
              Portfolio Dashboard
            </Heading>
            <HStack spacing={4}>
              <Badge
                colorScheme="brand"
                variant="subtle"
                px={3}
                py={1}
                borderRadius="full"
                fontSize="sm"
                fontWeight="600"
              >
                {filteredMsmes.length} of {msmes.length} MSMEs
              </Badge>
              <Text color="neutral.600" fontSize="sm">
                Last updated: {new Date().toLocaleDateString()}
              </Text>
            </HStack>
          </VStack>
          <Button
            leftIcon={<LucideIcon name="Plus" size={18} />}
            colorScheme="brand"
            size="lg"
            onClick={() => router.push('/msme/new')}
            shadow="sm"
            _hover={{
              shadow: 'md',
              transform: 'translateY(-1px)',
            }}
          >
            Add MSME
          </Button>
        </Flex>

        {/* Filters */}
        <Box
          bg="white"
          p={6}
          borderRadius="xl"
          shadow="sm"
          border="1px solid"
          borderColor="neutral.200"
          mb={6}
        >
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
            <InputGroup>
              <InputLeftElement pointerEvents="none">
                <LucideIcon name="Search" size={18} color="neutral.400" />
              </InputLeftElement>
              <Input
                placeholder="Search MSMEs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                bg="neutral.50"
                border="1px solid"
                borderColor="neutral.200"
                _focus={{
                  borderColor: 'brand.400',
                  bg: 'white',
                  shadow: '0 0 0 3px rgba(14, 165, 233, 0.1)',
                }}
                _hover={{
                  borderColor: 'neutral.300',
                }}
              />
            </InputGroup>

            <Select
              value={riskFilter}
              onChange={(e) => setRiskFilter(e.target.value)}
              bg="neutral.50"
              border="1px solid"
              borderColor="neutral.200"
              _focus={{
                borderColor: 'brand.400',
                bg: 'white',
                shadow: '0 0 0 3px rgba(14, 165, 233, 0.1)',
              }}
              _hover={{
                borderColor: 'neutral.300',
              }}
            >
              <option value="all">All Risk Bands</option>
              <option value="red">High Risk</option>
              <option value="yellow">Medium Risk</option>
              <option value="green">Low Risk</option>
            </Select>

            <Select
              value={businessTypeFilter}
              onChange={(e) => setBusinessTypeFilter(e.target.value)}
              bg="neutral.50"
              border="1px solid"
              borderColor="neutral.200"
              _focus={{
                borderColor: 'brand.400',
                bg: 'white',
                shadow: '0 0 0 3px rgba(14, 165, 233, 0.1)',
              }}
              _hover={{
                borderColor: 'neutral.300',
              }}
            >
              <option value="all">All Business Types</option>
              <option value="retail">Retail</option>
              <option value="b2b">B2B</option>
              <option value="services">Services</option>
              <option value="manufacturing">Manufacturing</option>
            </Select>
          </SimpleGrid>
        </Box>

        {/* MSME Table */}
        <Box
          bg="white"
          borderRadius="xl"
          shadow="sm"
          border="1px solid"
          borderColor="neutral.200"
          overflow="hidden"
        >
          <TableContainer>
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>Business</Th>
                  <Th>Location</Th>
                  <Th>Credit Score</Th>
                  <Th>Risk Band</Th>
                  <Th>Trend</Th>
                  <Th>Signals</Th>
                  <Th>Last Activity</Th>
                  <Th>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {filteredMsmes.map((msme) => (
                  <Tr
                    key={msme.msme_id}
                    cursor="pointer"
                    _hover={{
                      bg: 'neutral.50',
                      transform: 'translateY(-1px)',
                      shadow: 'sm',
                    }}
                    onClick={() => handleMSMEClick(msme.msme_id)}
                    transition="all 0.2s ease-in-out"
                  >
                    <Td>
                      <VStack align="start" spacing={2}>
                        <HStack spacing={3}>
                          <Box
                            p={2}
                            bg={getRiskBandBg(msme.risk_band)}
                            borderRadius="lg"
                            border="1px solid"
                            borderColor={getRiskBandColor(msme.risk_band)}
                          >
                            <LucideIcon
                              name={getBusinessTypeIcon(msme.business_type)}
                              size={16}
                              color={getRiskBandColor(msme.risk_band)}
                            />
                          </Box>
                          <VStack align="start" spacing={0}>
                            <Text fontWeight="600" color="neutral.800">
                              {msme.name}
                            </Text>
                            <Text fontSize="xs" color="neutral.500" textTransform="capitalize">
                              {msme.business_type}
                            </Text>
                          </VStack>
                        </HStack>
                      </VStack>
                    </Td>
                    <Td>
                      <Text color="neutral.600" fontSize="sm">
                        {msme.location}
                      </Text>
                    </Td>
                    <Td>
                      <VStack align="start" spacing={1}>
                        <Text
                          fontSize="xl"
                          fontWeight="700"
                          color={getRiskBandColor(msme.risk_band)}
                        >
                          {formatScore(msme.current_score)}
                        </Text>
                        <Text fontSize="xs" color="neutral.500">
                          Credit Score
                        </Text>
                      </VStack>
                    </Td>
                    <Td>
                      <Badge
                        bg={getRiskBandBg(msme.risk_band)}
                        color={getRiskBandColor(msme.risk_band)}
                        border="1px solid"
                        borderColor={getRiskBandColor(msme.risk_band)}
                        px={3}
                        py={1}
                        borderRadius="full"
                        fontSize="xs"
                        fontWeight="600"
                        textTransform="capitalize"
                      >
                        {msme.risk_band === 'green' ? 'Low' :
                         msme.risk_band === 'yellow' ? 'Medium' : 'High'}
                      </Badge>
                    </Td>
                    <Td>
                      <HStack spacing={2}>
                        <LucideIcon
                          name={getTrendIcon(msme.score_trend)}
                          size={16}
                          color={getTrendColor(msme.score_trend)}
                        />
                        <Text
                          fontSize="sm"
                          color={getTrendColor(msme.score_trend)}
                          textTransform="capitalize"
                          fontWeight="600"
                        >
                          {msme.score_trend}
                        </Text>
                      </HStack>
                    </Td>
                    <Td>
                      <Badge
                        colorScheme="brand"
                        variant="subtle"
                        borderRadius="full"
                        px={2}
                        py={1}
                        fontSize="xs"
                        fontWeight="600"
                      >
                        {msme.signals_count} signals
                      </Badge>
                    </Td>
                    <Td>
                      <VStack align="start" spacing={1}>
                        <Text fontSize="sm" color="neutral.600">
                          {msme.last_signal_date ? formatDate(msme.last_signal_date) : 'No signals'}
                        </Text>
                        <Text fontSize="xs" color="neutral.400">
                          Last activity
                        </Text>
                      </VStack>
                    </Td>
                    <Td>
                      <Button
                        size="sm"
                        variant="outline"
                        colorScheme="brand"
                        leftIcon={<LucideIcon name="BarChart" size={14} />}
                        borderRadius="lg"
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/msme/${msme.msme_id}/analytics`);
                        }}
                        _hover={{
                          bg: 'brand.50',
                          transform: 'translateY(-1px)',
                          shadow: 'sm',
                        }}
                      >
                        Analytics
                      </Button>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </TableContainer>
        </Box>

        {filteredMsmes.length === 0 && (
          <Box
            textAlign="center"
            py={12}
            bg="white"
            borderRadius="xl"
            shadow="sm"
            border="1px solid"
            borderColor="neutral.200"
          >
            <VStack spacing={4}>
              <LucideIcon name="Search" size={48} color="neutral.400" />
              <VStack spacing={2}>
                <Text fontSize="lg" fontWeight="600" color="neutral.600">
                  No MSMEs found
                </Text>
                <Text fontSize="sm" color="neutral.500">
                  Try adjusting your search criteria or filters
                </Text>
              </VStack>
            </VStack>
          </Box>
        )}
      </Container>
    </Box>
  );
}
