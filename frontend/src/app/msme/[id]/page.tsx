'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Badge,
  HStack,
  VStack,
  Spinner,
  Alert,
  AlertIcon,
  Button,
  Flex,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,

  Divider,
  Progress,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Textarea,
  Select,
  FormControl,
  FormLabel,
  useToast,
} from '@chakra-ui/react';
import { ArrowBackIcon, BellIcon } from '@chakra-ui/icons';
import { MSMEProfile, Signal, Nudge, ScoreDetails } from '@/types';
import { apiClient } from '@/lib/api';
import {
  getRiskBandColor,
  getRiskBandColorScheme,
  formatScore,
  formatDateTime,
  formatRelativeTime,
  getSignalSourceIcon,
  formatCurrency,
  formatNumber,
} from '@/lib/utils';

export default function MSMEDetailPage() {
  const params = useParams();
  const router = useRouter();
  const toast = useToast();
  const msmeId = params.id as string;
  
  const [msme, setMsme] = useState<MSMEProfile | null>(null);
  const [scoreDetails, setScoreDetails] = useState<ScoreDetails | null>(null);
  const [signals, setSignals] = useState<Signal[]>([]);
  const [nudges, setNudges] = useState<Nudge[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Nudge modal
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [nudgeMessage, setNudgeMessage] = useState('');
  const [nudgeMedium, setNudgeMedium] = useState('email');
  const [sendingNudge, setSendingNudge] = useState(false);
  
  const cardBg = 'white';
  const borderColor = 'gray.200';

  useEffect(() => {
    if (msmeId) {
      fetchMSMEData();
    }
  }, [msmeId]);

  const fetchMSMEData = async () => {
    try {
      setLoading(true);
      const [msmeData, scoreData, signalsData, nudgesData] = await Promise.all([
        apiClient.getMSME(msmeId),
        apiClient.getMSMEScore(msmeId),
        apiClient.getMSMESignals(msmeId),
        apiClient.getMSMENudges(msmeId),
      ]);
      
      setMsme(msmeData);
      setScoreDetails(scoreData);
      setSignals(signalsData);
      setNudges(nudgesData);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch MSME data');
    } finally {
      setLoading(false);
    }
  };

  const handleSendNudge = async () => {
    if (!nudgeMessage.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a message',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      setSendingNudge(true);
      await apiClient.sendNudge(msmeId, {
        message: nudgeMessage,
        trigger_type: 'manual',
        medium: nudgeMedium,
        metadata: { manual_trigger: true },
      });
      
      toast({
        title: 'Success',
        description: 'Nudge sent successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
      setNudgeMessage('');
      onClose();
      
      // Refresh nudges
      const updatedNudges = await apiClient.getMSMENudges(msmeId);
      setNudges(updatedNudges);
    } catch (err) {
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to send nudge',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setSendingNudge(false);
    }
  };

  if (loading) {
    return (
      <Container maxW="container.xl" py={8}>
        <Flex justify="center" align="center" minH="400px">
          <VStack spacing={4}>
            <Spinner size="xl" color="blue.500" />
            <Text>Loading MSME details...</Text>
          </VStack>
        </Flex>
      </Container>
    );
  }

  if (error || !msme || !scoreDetails) {
    return (
      <Container maxW="container.xl" py={8}>
        <Alert status="error">
          <AlertIcon />
          {error || 'MSME not found'}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxW="container.xl" py={8}>
      {/* Header */}
      <Flex justify="space-between" align="center" mb={8}>
        <HStack spacing={4}>
          <Button
            leftIcon={<ArrowBackIcon />}
            variant="ghost"
            onClick={() => router.back()}
          >
            Back
          </Button>
          <VStack align="start" spacing={1}>
            <Heading size="lg">{msme.name}</Heading>
            <Text color="gray.600">{msme.location}</Text>
          </VStack>
        </HStack>
        <HStack spacing={4}>
          <Badge
            colorScheme={getRiskBandColorScheme(msme.risk_band)}
            variant="solid"
            fontSize="md"
            px={3}
            py={1}
          >
            {msme.risk_band === 'green' ? 'Low' :
             msme.risk_band === 'yellow' ? 'Medium' : 'High'}
          </Badge>
          <Button
            leftIcon={<BellIcon />}
            colorScheme="blue"
            onClick={onOpen}
          >
            Send Nudge
          </Button>
        </HStack>
      </Flex>

      {/* Score Overview */}
      <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" mb={6}>
        <CardHeader>
          <Heading size="md">Credit Score Overview</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 4 }} spacing={6}>
            <VStack align="start">
              <Text fontSize="3xl" fontWeight="bold" color={getRiskBandColor(scoreDetails.risk_band)}>
                {formatScore(scoreDetails.current_score)}
              </Text>
              <Text color="gray.600">Current Score</Text>
            </VStack>
            
            <VStack align="start">
              <Text fontSize="lg" fontWeight="semibold">
                {scoreDetails.score_breakdown.base_score}
              </Text>
              <Text color="gray.600">Base Score</Text>
            </VStack>
            
            <VStack align="start">
              <Text fontSize="lg" fontWeight="semibold" color="red.500">
                -{scoreDetails.score_breakdown.gst_penalty + 
                  scoreDetails.score_breakdown.reviews_penalty + 
                  scoreDetails.score_breakdown.upi_penalty}
              </Text>
              <Text color="gray.600">Total Penalties</Text>
            </VStack>
            
            <VStack align="start">
              <Text fontSize="lg" fontWeight="semibold">
                {scoreDetails.signals_count}
              </Text>
              <Text color="gray.600">Data Signals</Text>
            </VStack>
          </SimpleGrid>
          
          {/* Score Breakdown */}
          <Divider my={4} />
          <VStack align="start" spacing={3}>
            <Text fontWeight="semibold">Score Breakdown:</Text>
            {Object.entries(scoreDetails.score_breakdown.details).map(([key, value]) => (
              <Text key={key} fontSize="sm" color="gray.600">
                • {value}
              </Text>
            ))}
          </VStack>
        </CardBody>
      </Card>

      {/* Tabs */}
      <Tabs variant="enclosed">
        <TabList>
          <Tab>Signal Timeline</Tab>
          <Tab>Nudge History</Tab>
        </TabList>

        <TabPanels>
          {/* Signals Tab */}
          <TabPanel px={0}>
            <VStack spacing={4} align="stretch">
              {signals.map((signal) => (
                <Card key={signal.signal_id} bg={cardBg} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <Flex justify="space-between" align="start">
                      <HStack spacing={3}>
                        <Text fontSize="lg">
                          {getSignalSourceIcon(signal.source)}
                        </Text>
                        <VStack align="start" spacing={1}>
                          <Text fontWeight="semibold" textTransform="uppercase">
                            {signal.source}
                          </Text>
                          <Text fontSize="sm" color="gray.600">
                            {formatDateTime(signal.timestamp)}
                          </Text>
                        </VStack>
                      </HStack>
                      <VStack align="end" spacing={1}>
                        <Text fontWeight="semibold">
                          {signal.source === 'gst' && typeof signal.value === 'number' 
                            ? formatCurrency(signal.value)
                            : signal.source === 'reviews' && typeof signal.value === 'object'
                            ? `${signal.value.review_count} reviews (${signal.value.average_rating}⭐)`
                            : signal.source === 'upi' && typeof signal.value === 'object'
                            ? `${signal.value.transaction_count} transactions`
                            : JSON.stringify(signal.value)
                          }
                        </Text>
                        {signal.normalized && (
                          <Text fontSize="sm" color="gray.600">
                            Normalized: {(signal.normalized * 100).toFixed(1)}%
                          </Text>
                        )}
                      </VStack>
                    </Flex>
                  </CardBody>
                </Card>
              ))}
              
              {signals.length === 0 && (
                <Box textAlign="center" py={8}>
                  <Text color="gray.500">No signals recorded yet</Text>
                </Box>
              )}
            </VStack>
          </TabPanel>

          {/* Nudges Tab */}
          <TabPanel px={0}>
            <VStack spacing={4} align="stretch">
              {nudges.map((nudge) => (
                <Card key={nudge.nudge_id} bg={cardBg} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <Flex justify="space-between" align="start">
                      <VStack align="start" spacing={2} flex={1}>
                        <HStack spacing={2}>
                          <Badge
                            colorScheme={
                              nudge.status === 'sent' || nudge.status === 'delivered' 
                                ? 'green' 
                                : nudge.status === 'failed' 
                                ? 'red' 
                                : 'yellow'
                            }
                            variant="solid"
                          >
                            {nudge.status.toUpperCase()}
                          </Badge>
                          <Text fontSize="sm" color="gray.600" textTransform="uppercase">
                            {nudge.medium}
                          </Text>
                          <Text fontSize="sm" color="gray.600">
                            {formatRelativeTime(nudge.sent_at)}
                          </Text>
                        </HStack>
                        <Text>{nudge.message}</Text>
                        <Text fontSize="sm" color="gray.600">
                          Trigger: {nudge.trigger_type.replace('_', ' ')}
                        </Text>
                      </VStack>
                    </Flex>
                  </CardBody>
                </Card>
              ))}
              
              {nudges.length === 0 && (
                <Box textAlign="center" py={8}>
                  <Text color="gray.500">No nudges sent yet</Text>
                </Box>
              )}
            </VStack>
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Send Nudge Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Send Manual Nudge</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl>
                <FormLabel>Medium</FormLabel>
                <Select value={nudgeMedium} onChange={(e) => setNudgeMedium(e.target.value)}>
                  <option value="email">Email</option>
                  <option value="whatsapp">WhatsApp</option>
                  <option value="sms">SMS</option>
                </Select>
              </FormControl>
              
              <FormControl>
                <FormLabel>Message</FormLabel>
                <Textarea
                  value={nudgeMessage}
                  onChange={(e) => setNudgeMessage(e.target.value)}
                  placeholder="Enter your message..."
                  rows={4}
                />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleSendNudge}
              isLoading={sendingNudge}
              loadingText="Sending..."
            >
              Send Nudge
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Container>
  );
}
