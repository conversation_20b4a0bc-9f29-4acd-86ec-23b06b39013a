'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Button,
  Flex,
  Spinner,
  Alert,
  AlertIcon,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  Divider,
  Icon,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
} from '@chakra-ui/react';
import { ArrowBackIcon } from '@chakra-ui/icons';
import { MSMEProfile, Signal, ScoreDetails } from '@/types';
import { apiClient } from '@/lib/api';
import {
  getRiskBandColor,
  getRiskBandColorScheme,
  formatScore,
  formatDate,
  getBusinessTypeIcon,
  getTrendIcon,
  getTrendColor,
} from '@/lib/utils';

export default function MSMEAnalyticsPage() {
  const params = useParams();
  const router = useRouter();
  const msmeId = params.id as string;

  const [msme, setMsme] = useState<MSMEProfile | null>(null);
  const [signals, setSignals] = useState<Signal[]>([]);
  const [scoreDetails, setScoreDetails] = useState<ScoreDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (msmeId) {
      fetchMSMEData();
    }
  }, [msmeId]);

  const fetchMSMEData = async () => {
    try {
      setLoading(true);
      const [msmeData, signalsData, scoreData] = await Promise.all([
        apiClient.getMSME(msmeId),
        apiClient.getMSMESignals(msmeId),
        apiClient.getMSMEScore(msmeId),
      ]);
      
      setMsme(msmeData);
      setSignals(signalsData);
      setScoreDetails(scoreData);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch MSME data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container maxW="container.xl" py={8}>
        <Flex justify="center" align="center" minH="400px">
          <VStack spacing={4}>
            <Spinner size="xl" color="blue.500" />
            <Text>Loading analytics...</Text>
          </VStack>
        </Flex>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxW="container.xl" py={8}>
        <Alert status="error">
          <AlertIcon />
          {error}
        </Alert>
      </Container>
    );
  }

  if (!msme) {
    return (
      <Container maxW="container.xl" py={8}>
        <Alert status="warning">
          <AlertIcon />
          MSME not found
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxW="container.xl" py={8}>
      {/* Header */}
      <VStack align="start" spacing={6} mb={8}>
        <Button
          leftIcon={<ArrowBackIcon />}
          variant="ghost"
          onClick={() => router.push('/portfolio')}
        >
          Back to Portfolio
        </Button>

        <Flex justify="space-between" align="start" w="full">
          <VStack align="start" spacing={2}>
            <HStack>
              <Icon as={() => getBusinessTypeIcon(msme.business_type)} />
              <Heading size="lg">{msme.name}</Heading>
            </HStack>
            <Text color="gray.600">{msme.location}</Text>
            <HStack spacing={4}>
              <Badge
                colorScheme={getRiskBandColorScheme(msme.risk_band)}
                variant="solid"
                fontSize="sm"
                px={3}
                py={1}
              >
                {msme.risk_band === 'green' ? 'Low' :
                 msme.risk_band === 'yellow' ? 'Medium' : 'High'}
              </Badge>
              <Text fontSize="sm" color="gray.600" textTransform="capitalize">
                {msme.business_type}
              </Text>
            </HStack>
          </VStack>
        </Flex>
      </VStack>

      {/* Key Metrics */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
        <Card>
          <CardBody>
            <Stat>
              <StatLabel>Credit Score</StatLabel>
              <StatNumber color={getRiskBandColor(msme.risk_band)}>
                {formatScore(msme.current_score)}
              </StatNumber>
              <StatHelpText>
                <HStack spacing={1}>
                  <Text color={getTrendColor(msme.score_trend)}>
                    {getTrendIcon(msme.score_trend)}
                  </Text>
                  <Text textTransform="capitalize">{msme.score_trend}</Text>
                </HStack>
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Stat>
              <StatLabel>Total Signals</StatLabel>
              <StatNumber>{msme.signals_count}</StatNumber>
              <StatHelpText>Data points collected</StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Stat>
              <StatLabel>Recent Nudges</StatLabel>
              <StatNumber>{msme.recent_nudges}</StatNumber>
              <StatHelpText>Last 7 days</StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Stat>
              <StatLabel>Last Activity</StatLabel>
              <StatNumber fontSize="md">
                {msme.last_signal_date ? formatDate(msme.last_signal_date) : 'No signals'}
              </StatNumber>
              <StatHelpText>Latest signal received</StatHelpText>
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Score Breakdown */}
      {scoreDetails && (
        <Card mb={8}>
          <CardHeader>
            <Heading size="md">Score Breakdown</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              {scoreDetails.breakdown?.map((component, index) => (
                <Box key={index}>
                  <Flex justify="space-between" mb={2}>
                    <Text fontWeight="semibold" textTransform="capitalize">
                      {component.source}
                    </Text>
                    <Text>{component.score.toFixed(0)}</Text>
                  </Flex>
                  <Progress
                    value={(component.score / 1000) * 100}
                    colorScheme={component.score > 700 ? 'green' : component.score > 400 ? 'yellow' : 'red'}
                    size="sm"
                  />
                </Box>
              ))}
            </VStack>
          </CardBody>
        </Card>
      )}

      {/* Recent Signals */}
      <Card>
        <CardHeader>
          <Heading size="md">Recent Signals</Heading>
        </CardHeader>
        <CardBody>
          {signals.length > 0 ? (
            <TableContainer>
              <Table variant="simple" size="sm">
                <Thead>
                  <Tr>
                    <Th>Source</Th>
                    <Th>Value</Th>
                    <Th>Normalized</Th>
                    <Th>Date</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {signals.slice(0, 10).map((signal) => (
                    <Tr key={signal.signal_id}>
                      <Td>
                        <Badge variant="outline" textTransform="uppercase">
                          {signal.source}
                        </Badge>
                      </Td>
                      <Td>
                        <Text fontSize="sm">
                          {typeof signal.value === 'object' 
                            ? JSON.stringify(signal.value) 
                            : signal.value.toString()}
                        </Text>
                      </Td>
                      <Td>
                        <Text fontSize="sm">
                          {signal.normalized ? (signal.normalized * 100).toFixed(1) + '%' : 'N/A'}
                        </Text>
                      </Td>
                      <Td>
                        <Text fontSize="sm">{formatDate(signal.timestamp)}</Text>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </TableContainer>
          ) : (
            <Text color="gray.500">No signals available</Text>
          )}
        </CardBody>
      </Card>
    </Container>
  );
}
