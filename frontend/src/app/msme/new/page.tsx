'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  Alert,
  AlertIcon,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Tag,
  TagLabel,
  TagCloseButton,
  Wrap,
  WrapItem,
} from '@chakra-ui/react';
import { ArrowBackIcon, AddIcon } from '@chakra-ui/icons';
import { apiClient } from '@/lib/api';

export default function NewMSMEPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    business_type: '',
    location: '',
    tags: [] as string[],
  });

  const [newTag, setNewTag] = useState('');

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.business_type || !formData.location) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const newMSME = await apiClient.createMSME(formData);
      setSuccess(true);
      
      // Redirect to the new MSME's analytics page after a short delay
      setTimeout(() => {
        router.push(`/msme/${newMSME.msme_id}/analytics`);
      }, 2000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create MSME');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Container maxW="container.md" py={8}>
        <Alert status="success">
          <AlertIcon />
          MSME created successfully! Redirecting to analytics page...
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxW="container.md" py={8}>
      <VStack align="start" spacing={6}>
        <Button
          leftIcon={<ArrowBackIcon />}
          variant="ghost"
          onClick={() => router.push('/portfolio')}
        >
          Back to Portfolio
        </Button>

        <Card w="full">
          <CardHeader>
            <Heading size="lg">Add New MSME</Heading>
            <Text color="gray.600">Create a new MSME profile for credit monitoring</Text>
          </CardHeader>
          
          <CardBody>
            <form onSubmit={handleSubmit}>
              <VStack spacing={6} align="stretch">
                {error && (
                  <Alert status="error">
                    <AlertIcon />
                    {error}
                  </Alert>
                )}

                <FormControl isRequired>
                  <FormLabel>Business Name</FormLabel>
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter business name"
                  />
                </FormControl>

                <FormControl isRequired>
                  <FormLabel>Business Type</FormLabel>
                  <Select
                    value={formData.business_type}
                    onChange={(e) => handleInputChange('business_type', e.target.value)}
                    placeholder="Select business type"
                  >
                    <option value="retail">Retail</option>
                    <option value="b2b">B2B</option>
                    <option value="services">Services</option>
                    <option value="manufacturing">Manufacturing</option>
                  </Select>
                </FormControl>

                <FormControl isRequired>
                  <FormLabel>Location</FormLabel>
                  <Input
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder="City, State"
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Tags</FormLabel>
                  <HStack mb={3}>
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add a tag"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addTag();
                        }
                      }}
                    />
                    <Button onClick={addTag} size="sm" leftIcon={<AddIcon />}>
                      Add
                    </Button>
                  </HStack>
                  
                  {formData.tags.length > 0 && (
                    <Wrap>
                      {formData.tags.map((tag) => (
                        <WrapItem key={tag}>
                          <Tag size="md" variant="solid" colorScheme="blue">
                            <TagLabel>{tag}</TagLabel>
                            <TagCloseButton onClick={() => removeTag(tag)} />
                          </Tag>
                        </WrapItem>
                      ))}
                    </Wrap>
                  )}
                </FormControl>

                <Flex justify="space-between" pt={4}>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/portfolio')}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    colorScheme="blue"
                    isLoading={loading}
                    loadingText="Creating..."
                  >
                    Create MSME
                  </Button>
                </Flex>
              </VStack>
            </form>
          </CardBody>
        </Card>
      </VStack>
    </Container>
  );
}
