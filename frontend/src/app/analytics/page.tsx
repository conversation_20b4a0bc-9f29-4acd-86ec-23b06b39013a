'use client';

import { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Progress,
  Badge,
  Spinner,
  Alert,
  AlertIcon,
  Flex,
  Divider,
} from '@chakra-ui/react';
import { MSMEProfile } from '@/types';
import { apiClient } from '@/lib/api';
import {
  getRiskBandColor,
  getRiskBandColorScheme,
  getRiskBandBg,
  formatScore,
} from '@/lib/utils';
import { Icon } from '@/components/ui/Icon';

interface PortfolioStats {
  totalMSMEs: number;
  averageScore: number;
  riskDistribution: {
    green: number;
    yellow: number;
    red: number;
  };
  businessTypeDistribution: {
    retail: number;
    b2b: number;
    services: number;
    manufacturing: number;
  };
  totalSignals: number;
  recentNudges: number;
}

export default function AnalyticsPage() {
  const [msmes, setMsmes] = useState<MSMEProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<PortfolioStats | null>(null);

  useEffect(() => {
    fetchPortfolioData();
  }, []);

  const fetchPortfolioData = async () => {
    try {
      setLoading(true);
      const data = await apiClient.getPortfolio();
      setMsmes(data);
      calculateStats(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch portfolio data');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (msmeData: MSMEProfile[]) => {
    const totalMSMEs = msmeData.length;
    const averageScore = totalMSMEs > 0 
      ? msmeData.reduce((sum, msme) => sum + msme.current_score, 0) / totalMSMEs 
      : 0;

    const riskDistribution = {
      green: msmeData.filter(m => m.risk_band === 'green').length,
      yellow: msmeData.filter(m => m.risk_band === 'yellow').length,
      red: msmeData.filter(m => m.risk_band === 'red').length,
    };

    const businessTypeDistribution = {
      retail: msmeData.filter(m => m.business_type === 'retail').length,
      b2b: msmeData.filter(m => m.business_type === 'b2b').length,
      services: msmeData.filter(m => m.business_type === 'services').length,
      manufacturing: msmeData.filter(m => m.business_type === 'manufacturing').length,
    };

    const totalSignals = msmeData.reduce((sum, msme) => sum + msme.signals_count, 0);
    const recentNudges = msmeData.reduce((sum, msme) => sum + msme.recent_nudges, 0);

    setStats({
      totalMSMEs,
      averageScore,
      riskDistribution,
      businessTypeDistribution,
      totalSignals,
      recentNudges,
    });
  };

  if (loading) {
    return (
      <Box bg="neutral.50" minH="100vh">
        <Container maxW="container.xl" py={8}>
          <Flex justify="center" align="center" minH="400px">
            <VStack spacing={6}>
              <Box
                w={16}
                h={16}
                bg="brand.100"
                borderRadius="full"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Spinner size="lg" color="brand.600" thickness="3px" />
              </Box>
              <VStack spacing={2}>
                <Text fontSize="lg" fontWeight="600" color="neutral.700">
                  Loading Analytics
                </Text>
                <Text fontSize="sm" color="neutral.500">
                  Analyzing your portfolio data...
                </Text>
              </VStack>
            </VStack>
          </Flex>
        </Container>
      </Box>
    );
  }

  if (error) {
    return (
      <Box bg="neutral.50" minH="100vh">
        <Container maxW="container.xl" py={8}>
          <Box
            bg="white"
            p={8}
            borderRadius="xl"
            shadow="sm"
            border="1px solid"
            borderColor="danger.200"
          >
            <Alert status="error" bg="danger.50" borderRadius="lg">
              <AlertIcon color="danger.600" />
              <VStack align="start" spacing={2}>
                <Text fontWeight="600" color="danger.800">
                  Failed to load analytics
                </Text>
                <Text fontSize="sm" color="danger.600">
                  {error}
                </Text>
              </VStack>
            </Alert>
          </Box>
        </Container>
      </Box>
    );
  }

  if (!stats) {
    return (
      <Box bg="neutral.50" minH="100vh">
        <Container maxW="container.xl" py={8}>
          <Box
            bg="white"
            p={8}
            borderRadius="xl"
            shadow="sm"
            border="1px solid"
            borderColor="warning.200"
          >
            <Alert status="warning" bg="warning.50" borderRadius="lg">
              <AlertIcon color="warning.600" />
              <VStack align="start" spacing={2}>
                <Text fontWeight="600" color="warning.800">
                  No data available
                </Text>
                <Text fontSize="sm" color="warning.600">
                  No portfolio data found to analyze
                </Text>
              </VStack>
            </Alert>
          </Box>
        </Container>
      </Box>
    );
  }

  return (
    <Box bg="neutral.50" minH="100vh">
      <Container maxW="container.xl" py={8}>
        <VStack align="start" spacing={8}>
          <Box>
            <Heading size="xl" mb={3} color="neutral.800" fontWeight="700">
              Portfolio Analytics
            </Heading>
            <HStack spacing={4}>
              <Badge
                colorScheme="brand"
                variant="subtle"
                px={3}
                py={1}
                borderRadius="full"
                fontSize="sm"
                fontWeight="600"
              >
                {stats.totalMSMEs} MSMEs
              </Badge>
              <Text color="neutral.600" fontSize="sm">
                Last updated: {new Date().toLocaleDateString()}
              </Text>
            </HStack>
          </Box>

          {/* Key Metrics */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} w="full">
            <Card
              bg="white"
              borderRadius="xl"
              shadow="sm"
              border="1px solid"
              borderColor="neutral.200"
              _hover={{ shadow: "md", transform: "translateY(-2px)" }}
              transition="all 0.2s ease-in-out"
            >
              <CardBody p={6}>
                <HStack spacing={4} mb={4}>
                  <Box
                    p={3}
                    bg="brand.50"
                    borderRadius="lg"
                    border="1px solid"
                    borderColor="brand.200"
                  >
                    <Icon name="Users" size={20} color="brand.600" />
                  </Box>
                  <VStack align="start" spacing={0}>
                    <Text fontSize="sm" color="neutral.600" fontWeight="600">
                      Total MSMEs
                    </Text>
                    <Text fontSize="2xl" fontWeight="700" color="neutral.800">
                      {stats.totalMSMEs}
                    </Text>
                  </VStack>
                </HStack>
                <Text fontSize="xs" color="neutral.500">
                  Active businesses in portfolio
                </Text>
              </CardBody>
            </Card>

            <Card
              bg="white"
              borderRadius="xl"
              shadow="sm"
              border="1px solid"
              borderColor="neutral.200"
              _hover={{ shadow: "md", transform: "translateY(-2px)" }}
              transition="all 0.2s ease-in-out"
            >
              <CardBody p={6}>
                <HStack spacing={4} mb={4}>
                  <Box
                    p={3}
                    bg={getRiskBandBg(stats.averageScore >= 700 ? 'green' : stats.averageScore >= 400 ? 'yellow' : 'red')}
                    borderRadius="lg"
                    border="1px solid"
                    borderColor={getRiskBandColor(stats.averageScore >= 700 ? 'green' : stats.averageScore >= 400 ? 'yellow' : 'red')}
                  >
                    <Icon name="BarChart" size={20} color={getRiskBandColor(stats.averageScore >= 700 ? 'green' : stats.averageScore >= 400 ? 'yellow' : 'red')} />
                  </Box>
                  <VStack align="start" spacing={0}>
                    <Text fontSize="sm" color="neutral.600" fontWeight="600">
                      Average Score
                    </Text>
                    <Text
                      fontSize="2xl"
                      fontWeight="700"
                      color={getRiskBandColor(stats.averageScore >= 700 ? 'green' : stats.averageScore >= 400 ? 'yellow' : 'red')}
                    >
                      {formatScore(stats.averageScore)}
                    </Text>
                  </VStack>
                </HStack>
                <Text fontSize="xs" color="neutral.500">
                  Portfolio credit average
                </Text>
              </CardBody>
            </Card>

            <Card
              bg="white"
              borderRadius="xl"
              shadow="sm"
              border="1px solid"
              borderColor="neutral.200"
              _hover={{ shadow: "md", transform: "translateY(-2px)" }}
              transition="all 0.2s ease-in-out"
            >
              <CardBody p={6}>
                <HStack spacing={4} mb={4}>
                  <Box
                    p={3}
                    bg="success.50"
                    borderRadius="lg"
                    border="1px solid"
                    borderColor="success.200"
                  >
                    <Icon name="Activity" size={20} color="success.600" />
                  </Box>
                  <VStack align="start" spacing={0}>
                    <Text fontSize="sm" color="neutral.600" fontWeight="600">
                      Total Signals
                    </Text>
                    <Text fontSize="2xl" fontWeight="700" color="neutral.800">
                      {stats.totalSignals}
                    </Text>
                  </VStack>
                </HStack>
                <Text fontSize="xs" color="neutral.500">
                  Data points collected
                </Text>
              </CardBody>
            </Card>

            <Card
              bg="white"
              borderRadius="xl"
              shadow="sm"
              border="1px solid"
              borderColor="neutral.200"
              _hover={{ shadow: "md", transform: "translateY(-2px)" }}
              transition="all 0.2s ease-in-out"
            >
              <CardBody p={6}>
                <HStack spacing={4} mb={4}>
                  <Box
                    p={3}
                    bg="warning.50"
                    borderRadius="lg"
                    border="1px solid"
                    borderColor="warning.200"
                  >
                    <Icon name="Bell" size={20} color="warning.600" />
                  </Box>
                  <VStack align="start" spacing={0}>
                    <Text fontSize="sm" color="neutral.600" fontWeight="600">
                      Recent Nudges
                    </Text>
                    <Text fontSize="2xl" fontWeight="700" color="neutral.800">
                      {stats.recentNudges}
                    </Text>
                  </VStack>
                </HStack>
                <Text fontSize="xs" color="neutral.500">
                  Last 7 days
                </Text>
              </CardBody>
            </Card>
          </SimpleGrid>

          {/* Risk Distribution */}
          <Card
            w="full"
            bg="white"
            borderRadius="xl"
            shadow="sm"
            border="1px solid"
            borderColor="neutral.200"
          >
            <CardHeader pb={4}>
              <HStack spacing={3}>
                <Box
                  p={2}
                  bg="brand.50"
                  borderRadius="lg"
                  border="1px solid"
                  borderColor="brand.200"
                >
                  <Icon name="Shield" size={18} color="brand.600" />
                </Box>
                <Heading size="md" color="neutral.800" fontWeight="600">
                  Risk Distribution
                </Heading>
              </HStack>
            </CardHeader>
            <CardBody pt={0}>
            <VStack spacing={4} align="stretch">
              <HStack justify="space-between">
                <HStack>
                  <Badge colorScheme="green" variant="solid">GREEN</Badge>
                  <Text>Low Risk</Text>
                </HStack>
                <Text fontWeight="semibold">{stats.riskDistribution.green}</Text>
              </HStack>
              <Progress 
                value={(stats.riskDistribution.green / stats.totalMSMEs) * 100} 
                colorScheme="green" 
                size="lg"
              />

              <HStack justify="space-between">
                <HStack>
                  <Badge colorScheme="yellow" variant="solid">YELLOW</Badge>
                  <Text>Medium Risk</Text>
                </HStack>
                <Text fontWeight="semibold">{stats.riskDistribution.yellow}</Text>
              </HStack>
              <Progress 
                value={(stats.riskDistribution.yellow / stats.totalMSMEs) * 100} 
                colorScheme="yellow" 
                size="lg"
              />

              <HStack justify="space-between">
                <HStack>
                  <Badge colorScheme="red" variant="solid">RED</Badge>
                  <Text>High Risk</Text>
                </HStack>
                <Text fontWeight="semibold">{stats.riskDistribution.red}</Text>
              </HStack>
              <Progress 
                value={(stats.riskDistribution.red / stats.totalMSMEs) * 100} 
                colorScheme="red" 
                size="lg"
              />
            </VStack>
          </CardBody>
        </Card>

        {/* Business Type Distribution */}
        <Card w="full">
          <CardHeader>
            <Heading size="md">Business Type Distribution</Heading>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
              <VStack align="start" spacing={3}>
                <HStack justify="space-between" w="full">
                  <Text textTransform="capitalize">Retail</Text>
                  <Text fontWeight="semibold">{stats.businessTypeDistribution.retail}</Text>
                </HStack>
                <Progress 
                  value={(stats.businessTypeDistribution.retail / stats.totalMSMEs) * 100} 
                  colorScheme="blue" 
                  size="sm" 
                  w="full"
                />
              </VStack>

              <VStack align="start" spacing={3}>
                <HStack justify="space-between" w="full">
                  <Text textTransform="capitalize">B2B</Text>
                  <Text fontWeight="semibold">{stats.businessTypeDistribution.b2b}</Text>
                </HStack>
                <Progress 
                  value={(stats.businessTypeDistribution.b2b / stats.totalMSMEs) * 100} 
                  colorScheme="purple" 
                  size="sm" 
                  w="full"
                />
              </VStack>

              <VStack align="start" spacing={3}>
                <HStack justify="space-between" w="full">
                  <Text textTransform="capitalize">Services</Text>
                  <Text fontWeight="semibold">{stats.businessTypeDistribution.services}</Text>
                </HStack>
                <Progress 
                  value={(stats.businessTypeDistribution.services / stats.totalMSMEs) * 100} 
                  colorScheme="teal" 
                  size="sm" 
                  w="full"
                />
              </VStack>

              <VStack align="start" spacing={3}>
                <HStack justify="space-between" w="full">
                  <Text textTransform="capitalize">Manufacturing</Text>
                  <Text fontWeight="semibold">{stats.businessTypeDistribution.manufacturing}</Text>
                </HStack>
                <Progress 
                  value={(stats.businessTypeDistribution.manufacturing / stats.totalMSMEs) * 100} 
                  colorScheme="orange" 
                  size="sm" 
                  w="full"
                />
              </VStack>
            </SimpleGrid>
          </CardBody>
          </Card>
        </VStack>
      </Container>
    </Box>
  );
}
