export interface MSMEProfile {
  msme_id: string;
  name: string;
  business_type: 'retail' | 'b2b' | 'services' | 'manufacturing';
  location: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  score_trend: 'improving' | 'declining' | 'stable';
  signals_count: number;
  recent_nudges: number;
  last_signal_date?: string;
  created_at: string;
  tags: string[];
}

export interface Signal {
  signal_id: string;
  msme_id: string;
  source: 'gst' | 'upi' | 'reviews' | 'justdial' | 'instagram' | 'maps';
  value: any;
  normalized?: number;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface Nudge {
  nudge_id: string;
  msme_id: string;
  trigger_type: 'score_drop' | 'score_improvement' | 'new_signal' | 'risk_band_change' | 'manual';
  message: string;
  medium: 'whatsapp' | 'email' | 'sms' | 'push_notification';
  sent_at: string;
  status: 'pending' | 'sent' | 'failed' | 'delivered';
  metadata?: Record<string, any>;
}

export interface ScoreBreakdown {
  base_score: number;
  gst_penalty: number;
  reviews_penalty: number;
  upi_penalty: number;
  details: Record<string, string>;
}

export interface ScoreDetails {
  msme_id: string;
  msme_name: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  score_breakdown: ScoreBreakdown;
  signals_count: number;
  last_updated: string;
  breakdown?: {
    source: string;
    score: number;
    weight: number;
  }[];
}

export interface DashboardAnalytics {
  total_msmes: number;
  total_signals: number;
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  business_type_distribution: Record<string, number>;
  average_signals_per_msme: number;
  last_updated: string;
}
