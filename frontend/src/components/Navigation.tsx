'use client';

import {
  Box,
  Flex,
  HStack,
  Link,
  Text,
  Container,
  Spacer,
  Badge,
} from '@chakra-ui/react';
import NextLink from 'next/link';
import { usePathname } from 'next/navigation';
import { Icon } from '@/components/ui/Icon';

export default function Navigation() {
  const pathname = usePathname();
  const isActive = (path: string) => pathname === path;

  return (
    <Box
      bg="white"
      borderBottom="1px"
      borderColor="neutral.200"
      position="sticky"
      top={0}
      zIndex={10}
      shadow="sm"
    >
      <Container maxW="container.xl">
        <Flex h={16} alignItems="center">
          <HStack spacing={8} alignItems="center">
            <Link as={NextLink} href="/portfolio" _hover={{ textDecoration: 'none' }}>
              <HStack spacing={2}>
                <Box
                  w={8}
                  h={8}
                  bg="brand.600"
                  borderRadius="lg"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Icon name="Shield" size={18} color="white" />
                </Box>
                <Text fontSize="xl" fontWeight="bold" color="neutral.800">
                  Credit Chakra
                </Text>
              </HStack>
            </Link>

            <HStack as="nav" spacing={1}>
              <Link
                as={NextLink}
                href="/portfolio"
                px={4}
                py={2}
                rounded="lg"
                fontWeight="600"
                fontSize="sm"
                bg={isActive('/portfolio') ? 'brand.50' : 'transparent'}
                color={isActive('/portfolio') ? 'brand.700' : 'neutral.600'}
                border="1px solid"
                borderColor={isActive('/portfolio') ? 'brand.200' : 'transparent'}
                _hover={{
                  textDecoration: 'none',
                  bg: isActive('/portfolio') ? 'brand.100' : 'neutral.50',
                  color: isActive('/portfolio') ? 'brand.800' : 'brand.600',
                }}
                transition="all 0.2s ease-in-out"
              >
                <HStack spacing={2}>
                  <Icon name="Users" size={16} />
                  <Text>Portfolio</Text>
                </HStack>
              </Link>

              <Link
                as={NextLink}
                href="/analytics"
                px={4}
                py={2}
                rounded="lg"
                fontWeight="600"
                fontSize="sm"
                bg={isActive('/analytics') ? 'brand.50' : 'transparent'}
                color={isActive('/analytics') ? 'brand.700' : 'neutral.600'}
                border="1px solid"
                borderColor={isActive('/analytics') ? 'brand.200' : 'transparent'}
                _hover={{
                  textDecoration: 'none',
                  bg: isActive('/analytics') ? 'brand.100' : 'neutral.50',
                  color: isActive('/analytics') ? 'brand.800' : 'brand.600',
                }}
                transition="all 0.2s ease-in-out"
              >
                <HStack spacing={2}>
                  <Icon name="BarChart" size={16} />
                  <Text>Analytics</Text>
                </HStack>
              </Link>
            </HStack>
          </HStack>

          <Spacer />

          <HStack spacing={4}>
            <Badge
              colorScheme="brand"
              variant="subtle"
              px={3}
              py={1}
              borderRadius="full"
              fontSize="xs"
              fontWeight="600"
            >
              Credit Manager
            </Badge>
          </HStack>
        </Flex>
      </Container>
    </Box>
  );
}
