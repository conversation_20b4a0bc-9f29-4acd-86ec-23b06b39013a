import { format, formatDistanceToNow, parseISO } from 'date-fns';

export function getRiskBandColor(riskBand: 'green' | 'yellow' | 'red'): string {
  switch (riskBand) {
    case 'green':
      return 'success.600';
    case 'yellow':
      return 'warning.600';
    case 'red':
      return 'danger.600';
    default:
      return 'neutral.500';
  }
}

export function getRiskBandColorScheme(riskBand: 'green' | 'yellow' | 'red'): string {
  switch (riskBand) {
    case 'green':
      return 'success';
    case 'yellow':
      return 'warning';
    case 'red':
      return 'danger';
    default:
      return 'neutral';
  }
}

export function getRiskBandBg(riskBand: 'green' | 'yellow' | 'red'): string {
  switch (riskBand) {
    case 'green':
      return 'success.50';
    case 'yellow':
      return 'warning.50';
    case 'red':
      return 'danger.50';
    default:
      return 'neutral.50';
  }
}

export function formatScore(score: number): string {
  return Math.round(score).toString();
}

export function formatDate(dateString: string): string {
  try {
    const date = parseISO(dateString);
    return format(date, 'MMM dd, yyyy');
  } catch {
    return 'Invalid date';
  }
}

export function formatDateTime(dateString: string): string {
  try {
    const date = parseISO(dateString);
    return format(date, 'MMM dd, yyyy HH:mm');
  } catch {
    return 'Invalid date';
  }
}

export function formatRelativeTime(dateString: string): string {
  try {
    const date = parseISO(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  } catch {
    return 'Invalid date';
  }
}

// Business type icon mapping for modern UI
export function getBusinessTypeIcon(businessType: string): string {
  switch (businessType) {
    case 'retail':
      return 'Store';
    case 'b2b':
      return 'Building';
    case 'services':
      return 'Tool';
    case 'manufacturing':
      return 'Factory';
    default:
      return 'Store';
  }
}

// Signal source icon mapping for modern UI
export function getSignalSourceIcon(source: string): string {
  switch (source) {
    case 'gst':
      return 'BarChart';
    case 'upi':
      return 'CreditCard';
    case 'reviews':
      return 'Star';
    case 'justdial':
      return 'Phone';
    case 'instagram':
      return 'Camera';
    case 'maps':
      return 'MapPin';
    default:
      return 'TrendingUp';
  }
}

// Trend icon mapping for modern UI
export function getTrendIcon(trend: 'improving' | 'declining' | 'stable'): string {
  switch (trend) {
    case 'improving':
      return 'TrendingUp';
    case 'declining':
      return 'TrendingDown';
    case 'stable':
      return 'Minus';
    default:
      return 'Minus';
  }
}

export function getTrendColor(trend: 'improving' | 'declining' | 'stable'): string {
  switch (trend) {
    case 'improving':
      return 'success.600';
    case 'declining':
      return 'danger.600';
    case 'stable':
      return 'neutral.500';
    default:
      return 'neutral.500';
  }
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

export function formatNumber(num: number): string {
  if (num >= 10000000) {
    return `${(num / 10000000).toFixed(1)}Cr`;
  } else if (num >= 100000) {
    return `${(num / 100000).toFixed(1)}L`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
}
