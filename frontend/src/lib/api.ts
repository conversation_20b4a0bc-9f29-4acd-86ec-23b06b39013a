import { MSMEProfile, Signal, Nudge, ScoreDetails, DashboardAnalytics } from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

class ApiClient {
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Portfolio endpoints
  async getPortfolio(): Promise<MSMEProfile[]> {
    return this.request<MSMEProfile[]>('/dashboard/portfolio');
  }

  async getDashboardAnalytics(): Promise<DashboardAnalytics> {
    return this.request<DashboardAnalytics>('/dashboard/analytics');
  }

  // MSME endpoints
  async getMSME(msmeId: string): Promise<MSMEProfile> {
    return this.request<MSMEProfile>(`/msme/${msmeId}`);
  }

  async createMSME(data: {
    name: string;
    business_type: string;
    location: string;
    tags?: string[];
  }): Promise<MSMEProfile> {
    return this.request<MSMEProfile>('/msme/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getMSMEScore(msmeId: string): Promise<ScoreDetails> {
    return this.request<ScoreDetails>(`/msme/${msmeId}/score`);
  }

  // Signal endpoints
  async getMSMESignals(msmeId: string, source?: string): Promise<Signal[]> {
    const params = source ? `?source=${source}` : '';
    return this.request<Signal[]>(`/api/signals/${msmeId}${params}`);
  }

  async addSignal(msmeId: string, signalData: {
    source: string;
    value: any;
    metadata?: Record<string, any>;
  }): Promise<any> {
    return this.request(`/msme/${msmeId}/signals`, {
      method: 'POST',
      body: JSON.stringify(signalData),
    });
  }

  // Nudge endpoints
  async getMSMENudges(msmeId: string, status?: string): Promise<Nudge[]> {
    const params = status ? `?status_filter=${status}` : '';
    return this.request<Nudge[]>(`/api/nudges/${msmeId}${params}`);
  }

  async sendNudge(msmeId: string, nudgeData: {
    message: string;
    trigger_type: string;
    medium: string;
    metadata?: Record<string, any>;
  }): Promise<any> {
    return this.request(`/msme/${msmeId}/nudge`, {
      method: 'POST',
      body: JSON.stringify(nudgeData),
    });
  }

  // Health check
  async healthCheck(): Promise<{ status: string; service: string }> {
    return this.request('/health');
  }
}

export const apiClient = new ApiClient();
